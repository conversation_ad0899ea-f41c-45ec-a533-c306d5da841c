import pytest
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON>ck
from sqlalchemy.orm import Session
from decimal import Decimal
from datetime import datetime

from src.core.services.general.user_service import UserService
from src.core.services.general.component_service import ComponentService
from src.core.services.general.component_category_service import ComponentCategoryService
from src.core.services.general.component_type_service import ComponentTypeService
from src.core.repositories.general.component_repository import ComponentRepository
from src.core.repositories.general.component_category_repository import ComponentCategoryRepository
from src.core.repositories.general.component_type_repository import ComponentTypeRepository
from src.core.schemas.general.component_schemas import ComponentCreateSchema
from src.core.schemas.general.component_category_schemas import ComponentCategoryCreateSchema
from src.core.schemas.general.component_type_schemas import ComponentTypeCreateSchema
from src.core.models.general.component import Component
from src.core.enums.electrical_enums import ComponentType as ComponentTypeEnum, ComponentCategoryType

@pytest.fixture
def db_session() -> MagicMock:
    """Fixture for a mocked database session."""
    return MagicMock(spec=Session)

@pytest.fixture
def user_service(db_session: MagicMock) -> UserService:
    """Fixture for the UserService."""
    return UserService(db_session=db_session)

@pytest.fixture
def mock_component_service():
    """Mock component service for testing."""
    service = MagicMock()
    service.create_component = MagicMock()
    service.get_component = MagicMock()
    service.update_component = MagicMock()
    service.delete_component = MagicMock()
    service.search_components = MagicMock()
    service.get_components_by_category = MagicMock()
    service.get_components_by_type = MagicMock()
    service.get_preferred_components = MagicMock()
    service.get_component_stats = MagicMock()
    return service

@pytest.fixture
def mock_component_repo(db_session):
    """Mock component repository."""
    repo = Mock(spec=ComponentRepository)
    repo.db_session = db_session
    return repo

@pytest.fixture
def component_service(mock_component_repo):
    """Component service instance with mocked dependencies."""
    return ComponentService(mock_component_repo)

@pytest.fixture
def sample_component_data():
    """Sample component creation data."""
    return ComponentCreateSchema(
        name="Test Circuit Breaker",
        manufacturer="ABB",
        model_number="S203-C16",
        description="16A Circuit Breaker",
        component_type_id=1,
        component_category_id=1,
        component_type=ComponentTypeEnum.CIRCUIT_BREAKER,
        category=ComponentCategoryType.PROTECTION_DEVICES,
        specifications={
            "electrical": {
                "current_rating": "16A",
                "voltage_rating": "230V",
                "breaking_capacity": "6kA"
            },
            "standards_compliance": ["IEC-60898-1"]
        },
        unit_price=Decimal("25.50"),
        currency="EUR",
        supplier="Electrical Supply Co",
        part_number="ABB-S203-C16",
        weight_kg=0.15,
        dimensions=None,
        is_active=True,
        is_preferred=False,
        stock_status="available",
        version="1.0",
        metadata=None
    )

@pytest.fixture
def sample_component_model():
    """Sample component model instance."""
    component = Mock(spec=Component)
    component.id = 1
    component.name = "Test Circuit Breaker"
    component.manufacturer = "ABB"
    component.model_number = "S203-C16"
    component.description = "16A Circuit Breaker"
    component.component_type = ComponentTypeEnum.CIRCUIT_BREAKER
    component.category = ComponentCategoryType.PROTECTION_DEVICES
    component.specifications = '{"electrical": {"current_rating": "16A"}}'
    component.unit_price = Decimal("25.50")
    component.currency = "EUR"
    component.supplier = "Electrical Supply Co"
    component.part_number = "ABB-S203-C16"
    component.weight_kg = 0.15
    component.dimensions_json = None
    component.is_active = True
    component.is_preferred = False
    component.is_deleted = False
    component.stock_status = "available"
    component.version = "1.0"
    component.metadata_json = None
    component.created_at = datetime(2024, 1, 1)
    component.updated_at = datetime(2024, 1, 1)
    component.full_name = "ABB S203-C16"
    component.display_name = "Test Circuit Breaker (ABB S203-C16)"
    mock_table = Mock()
    mock_columns = [Mock(name=field) for field in ['id', 'name', 'manufacturer', 'model_number', 'description', 'component_type', 'category', 'specifications', 'unit_price', 'currency', 'supplier', 'part_number', 'weight_kg', 'dimensions_json', 'is_active', 'is_preferred', 'is_deleted', 'stock_status', 'version', 'metadata_json', 'created_at', 'updated_at']]
    mock_table.columns = mock_columns
    component.__table__ = mock_table
    return component

@pytest.fixture
def mock_category_repository() -> Mock:
    """Create mock repository for categories."""
    return Mock(spec=ComponentCategoryRepository)

@pytest.fixture
def category_service(mock_category_repository: Mock) -> ComponentCategoryService:
    """Create ComponentCategoryService instance."""
    return ComponentCategoryService(mock_category_repository)

@pytest.fixture
def sample_category_data() -> ComponentCategoryCreateSchema:
    """Create sample category data."""
    return ComponentCategoryCreateSchema(
        name="Test Category",
        description="Test description",
        is_active=True,
        parent_category_id=None
    )

@pytest.fixture
def mock_type_repository() -> Mock:
    """Create mock repository for types."""
    return Mock(spec=ComponentTypeRepository)

@pytest.fixture
def type_service(mock_type_repository: Mock) -> ComponentTypeService:
    """Create ComponentTypeService instance."""
    return ComponentTypeService(mock_type_repository)

@pytest.fixture
def sample_type_data() -> ComponentTypeCreateSchema:
    """Create sample component type data."""
    return ComponentTypeCreateSchema(
        name="Test Type",
        description="Test description",
        category_id=1,
        is_active=True,
        specifications_template={},
        metadata={}
    )